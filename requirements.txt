# AI Meeting Transcription & Upsell/Cross-sell System Dependencies

# Core Web Framework
Flask==3.0.0
Flask-CORS==4.0.0
Werkzeug==3.0.1

# Machine Learning & AI
scikit-learn==1.3.2
pandas==2.1.4
numpy==1.24.3
transformers==4.36.2
torch==2.1.2
torchaudio==2.1.2

# Audio Processing
librosa==0.10.1
soundfile==0.12.1
pydub==0.25.1
speech-recognition==3.10.0
openai-whisper==20231117
ffmpeg-python==0.2.0
mutagen==1.47.0

# Data Processing & Visualization
matplotlib==3.8.2
seaborn==0.13.0
plotly==5.17.0

# API & HTTP
requests==2.31.0
httpx==0.25.2
urllib3==2.1.0
openai>=1.0.0

# File Processing
openpyxl==3.1.2
python-docx==1.1.0

# Utilities
python-dotenv==1.0.0
tqdm==4.66.1
joblib==1.3.2
colorama==0.4.6
click==8.1.7

# Development & Testing
pytest==7.4.3
black==23.11.0
flake8==6.1.0

# Text Processing
nltk==3.8.1

# Date & Time
python-dateutil==2.8.2

# Logging
loguru==0.7.2

# Progress Bars
rich==13.7.0

# HTTP Server
gunicorn==21.2.0
waitress==2.1.2

# System Utilities
psutil==5.9.6

# Security
cryptography==41.0.8

# JSON & Data Serialization
jsonschema==4.20.0

# Environment & Configuration
pyyaml==6.0.1

# API Documentation
flask-restx==1.3.0
marshmallow==3.20.1

# Database Support
SQLAlchemy==2.0.23

# Image Processing
Pillow==10.1.0

# Memory Management
memory-profiler==0.61.0

# File Watching
watchdog==3.0.0

# Timezone Support
pytz==2023.3

# Math & Statistics
scipy==1.11.4
statsmodels==0.14.0

# Caching
cachetools==5.3.2

# Validation
cerberus==1.3.5

# String Processing
fuzzywuzzy==0.18.0
python-Levenshtein==0.23.0

# Web Scraping
beautifulsoup4==4.12.2
lxml==4.9.3

# Email Support
email-validator==2.1.0

# Network Utilities
ping3==4.0.4

# Type Hints
typing-extensions==4.8.0
