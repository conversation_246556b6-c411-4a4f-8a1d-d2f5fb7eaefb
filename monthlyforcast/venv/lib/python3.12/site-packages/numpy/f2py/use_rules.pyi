from collections.abc import Mapping
from typing import Any, Final

__version__: Final[str] = ...
f2py_version: Final = "See `f2py -v`"
usemodule_rules: Final[dict[str, str | list[str]]] = ...

def buildusevars(m: Mapping[str, object], r: Mapping[str, Mapping[str, object]]) -> dict[str, Any]: ...
def buildusevar(name: str, realname: str, vars: Mapping[str, Mapping[str, object]], usemodulename: str) -> dict[str, Any]: ...
