../../../bin/whisper,sha256=3hIAjST8JaCz8oikE9wC7rMOHIGawODUjZu-bGF6weU,324
openai_whisper-20250625.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
openai_whisper-20250625.dist-info/METADATA,sha256=D-PBmh0Ewb7pCoYJj_KV9EunHW58kyEEqzGoY0VFCe0,9385
openai_whisper-20250625.dist-info/RECORD,,
openai_whisper-20250625.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
openai_whisper-20250625.dist-info/WHEEL,sha256=_zCd3N1l69ArxyTb8rzEoP9TpbYXkqRFSNOD5OuxnTs,91
openai_whisper-20250625.dist-info/direct_url.json,sha256=hWZvOkBm3SelUoj30fHeFLljMytThap1ma5xv-4h_6Y,133
openai_whisper-20250625.dist-info/entry_points.txt,sha256=1OTVp0DSZHyPxFEkpZPbYPXpKAw2SsO-M4Ap-VLrSlQ,51
openai_whisper-20250625.dist-info/licenses/LICENSE,sha256=tdZaWQYOaMT_lA4e3fpvlLLWj99Y7X9N1XchyZfjXp0,1063
openai_whisper-20250625.dist-info/top_level.txt,sha256=e73_hD7PzIrqq5Qkj8xc0C3FVPiu7AOQae35YeSGDfQ,8
whisper/__init__.py,sha256=eey-1xR4O2Iwkxo9um1dptOG4tRPn8JyVuT8cnUeH2I,7432
whisper/__main__.py,sha256=xRnVhsOmtjn2TlnS1v1rWRpn7Nc5-XJ0kpEfxM5NVFo,35
whisper/__pycache__/__init__.cpython-312.pyc,,
whisper/__pycache__/__main__.cpython-312.pyc,,
whisper/__pycache__/audio.cpython-312.pyc,,
whisper/__pycache__/decoding.cpython-312.pyc,,
whisper/__pycache__/model.cpython-312.pyc,,
whisper/__pycache__/timing.cpython-312.pyc,,
whisper/__pycache__/tokenizer.cpython-312.pyc,,
whisper/__pycache__/transcribe.cpython-312.pyc,,
whisper/__pycache__/triton_ops.cpython-312.pyc,,
whisper/__pycache__/utils.cpython-312.pyc,,
whisper/__pycache__/version.cpython-312.pyc,,
whisper/assets/gpt2.tiktoken,sha256=MGzSfwPBpxTspxCOA9ZrfcBCq-jCWLRMGZp-2YON2TA,835554
whisper/assets/mel_filters.npz,sha256=dFCucHI6XvnTQePO5ijHywF382zkLES37SvzMl8PbUw,4271
whisper/assets/multilingual.tiktoken,sha256=s0s2DbtJPngeR5eUWG1mFwBnDWVWQAHyMCSXHR8voSY,816730
whisper/audio.py,sha256=Nu0oey3j6vQCB80qRKjUXVfqXCEqOd8HQB1Py01aWho,4945
whisper/decoding.py,sha256=lAhP7cWvdMq_jhiDqFVScABXCB8A6maa1JQ17LKQOds,32155
whisper/model.py,sha256=RzSRqXVUrR0L6q72_pHZZfEnhbRmuT67YQJDB7UK1oM,11749
whisper/normalizers/__init__.py,sha256=8Y_Nzkyu5_LoDkwBqwRX06CAqHKilIlULG0jxLC7pXI,130
whisper/normalizers/__pycache__/__init__.cpython-312.pyc,,
whisper/normalizers/__pycache__/basic.cpython-312.pyc,,
whisper/normalizers/__pycache__/english.cpython-312.pyc,,
whisper/normalizers/basic.py,sha256=R0LqoEDgZX-hJHoTYeDYVsYjF6QzJupZpAwunt2NLDg,2064
whisper/normalizers/english.json,sha256=Zgf5SL6YJNLhsvoiI82UwGxFr6TgXqDj1eHyvf_eJGU,56128
whisper/normalizers/english.py,sha256=KJjkZyru_J9Ey03jjBFc3yhvWzWuMhQzRnp2dM6IQdA,20868
whisper/timing.py,sha256=uwkclzxChpcBLw-NG09fOmBc2TxdHBKZV_cSpqh4pZw,12697
whisper/tokenizer.py,sha256=O0jjYafpW07ANWym1yu6Y1d4qhAmkVMTbue8NMrjC4U,12338
whisper/transcribe.py,sha256=hIuCh3UA1vuPC5G-56CKxn72cKETWR0RtJSEjSY60PY,30366
whisper/triton_ops.py,sha256=JsUILwKbvSt6537PWj9_GW8iDlhQ4snQ7EKWHK8N3kw,3646
whisper/utils.py,sha256=wlmQRGq33VujC9Ur1PDmU38RteNhoBXwLCE1XUECewU,11529
whisper/version.py,sha256=xNJ-zM3VKpze12LxqC2jFS2xLLwKpup4H5RURqFDz68,25
